# 🔥 Firebase Authentication Flutter Implementation Guide

## 📋 Overview

This guide documents the Firebase Authentication implementation in the BanaChef Flutter app, providing a unified authentication system that works with the updated server architecture.

## 🏗️ Architecture

### Authentication Flow
```
User Action → Firebase Auth → Firebase ID Token → Server → JWT Token → App State
```

### Key Components

1. **FirebaseAuthService** - Primary authentication service
2. **AuthRepository** - API communication layer  
3. **AuthCubit** - State management
4. **Legacy Services** - Backward compatibility

## 🚀 Quick Start

### 1. Using Firebase Authentication

```dart
// Inject FirebaseAuthService
final firebaseAuthService = getIt<FirebaseAuthService>();

// Google Sign-In with Firebase
try {
  final firebaseToken = await firebaseAuthService.signInWithGoogle();
  print('Firebase ID Token: $firebaseToken');
} catch (e) {
  print('Authentication failed: $e');
}

// Apple Sign-In with Firebase (iOS only)
try {
  final firebaseToken = await firebaseAuthService.signInWithApple();
  print('Firebase ID Token: $firebaseToken');
} catch (e) {
  print('Authentication failed: $e');
}
```

### 2. Using AuthCubit (Recommended)

```dart
// Inject AuthCubit
final authCubit = getIt<AuthCubit>();

// Google Sign-In (Firebase-based)
await authCubit.signInWithGoogleFirebase();

// Apple Sign-In (Firebase-based)
await authCubit.signInWithAppleFirebase();

// Listen to auth state
BlocListener<AuthCubit, AuthState>(
  listener: (context, state) {
    if (state is AuthAuthenticated) {
      // User is authenticated
      print('User: ${state.user.email}');
    } else if (state is AuthError) {
      // Handle error
      print('Error: ${state.message}');
    }
  },
  child: YourWidget(),
)
```

## 🔄 Migration from Legacy Auth

### Before (Legacy OAuth)
```dart
// Old way - direct OAuth tokens
final googleAuthService = getIt<GoogleAuthService>();
final idToken = await googleAuthService.signIn();
final authResponse = await authRepository.loginWithGoogle(idToken);
```

### After (Firebase Auth)
```dart
// New way - Firebase ID tokens
final firebaseAuthService = getIt<FirebaseAuthService>();
final firebaseToken = await firebaseAuthService.signInWithGoogle();
final authResponse = await authRepository.loginWithFirebase(firebaseToken);
```

### Using AuthCubit (Recommended)
```dart
// Recommended - Use Cubit methods
final authCubit = getIt<AuthCubit>();
await authCubit.signInWithGoogleFirebase(); // New Firebase method
// or
await authCubit.signInWithGoogle(); // Legacy method (still works)
```

## 📱 Platform-Specific Setup

### Android
- ✅ Firebase configuration already set up
- ✅ `google-services.json` configured
- ✅ Google Sign-In enabled

### iOS  
- ✅ Firebase configuration already set up
- ✅ `GoogleService-Info.plist` configured
- ✅ Apple Sign-In enabled

## 🔧 Configuration

### Firebase Project
- **Project ID**: `banachef-f03aa`
- **Google Sign-In**: Enabled
- **Apple Sign-In**: Enabled
- **Email Verification**: Enforced by server

### App Configuration
```dart
// lib/core/config/app_config.dart
static String get googleClientId {
  switch (currentEnvironment) {
    case Environment.development:
      return '418965755087-8ulqb0lq7fgvofmpgrdaia3c6ho7u1mc.apps.googleusercontent.com';
    case Environment.production:
      return '418965755087-8ulqb0lq7fgvofmpgrdaia3c6ho7u1mc.apps.googleusercontent.com';
  }
}
```

## 🔐 Security Features

### Token Management
- **Firebase ID Tokens**: Short-lived, automatically refreshed
- **JWT Access Tokens**: 30-minute expiration
- **Refresh Tokens**: 7-day expiration
- **Secure Storage**: All tokens stored securely

### Email Verification
```dart
// Check email verification status
if (firebaseAuthService.isEmailVerified) {
  // Email is verified
} else {
  // Send verification email
  await firebaseAuthService.sendEmailVerification();
}
```

### Sign Out
```dart
// Complete sign out (Firebase + local data)
await authCubit.signOut();
// or
await firebaseAuthService.signOut();
await authRepository.logout();
```

## 🧪 Testing

### Unit Tests
```bash
# Run Firebase Auth service tests
flutter test test/features/auth/services/firebase_auth_service_test.dart
```

### Integration Tests
```bash
# Run integration test script
dart scripts/test_firebase_auth_integration.dart
```

## 🐛 Troubleshooting

### Common Issues

1. **Firebase not initialized**
   ```
   Error: Firebase has not been initialized
   ```
   **Solution**: Ensure `Firebase.initializeApp()` is called in `main.dart`

2. **Google Sign-In cancelled**
   ```
   Error: Google sign in was cancelled
   ```
   **Solution**: User cancelled the sign-in flow, handle gracefully

3. **Apple Sign-In not available**
   ```
   Error: Apple Sign In is only available on iOS
   ```
   **Solution**: Check platform before calling Apple Sign-In

4. **Email not verified**
   ```
   Error: Email verification required
   ```
   **Solution**: Send verification email and ask user to verify

### Debug Logging
```dart
// Enable debug logging in FirebaseAuthService
print('🔥 Firebase sign-in successful: ${userCredential.user!.email}');
```

## 📊 API Endpoints

### New Firebase Endpoint
```
POST /api/v1/auth/login/firebase
Content-Type: application/json

{
  "firebase_token": "eyJhbGciOiJSUzI1NiIs..."
}
```

### Legacy Endpoints (Still Supported)
```
POST /api/v1/auth/login/google
POST /api/v1/auth/login/apple
POST /api/v1/auth/refresh
```

## 🔄 Backward Compatibility

The implementation maintains full backward compatibility:

- ✅ Existing OAuth methods still work
- ✅ Legacy AuthCubit methods unchanged
- ✅ Existing UI components work without changes
- ✅ Gradual migration possible

## 🎯 Best Practices

1. **Use Firebase Methods**: Prefer `signInWithGoogleFirebase()` over legacy methods
2. **Handle Errors**: Always wrap auth calls in try-catch blocks
3. **Check Email Verification**: Verify email status after authentication
4. **Secure Token Storage**: Use provided TokenService for token management
5. **Test Thoroughly**: Test on both iOS and Android devices

## 📈 Performance Benefits

- **Faster Authentication**: Firebase handles OAuth complexity
- **Better Error Handling**: Unified error responses
- **Automatic Token Refresh**: Firebase manages token lifecycle
- **Reduced Server Load**: Firebase handles authentication processing

## 🔮 Future Enhancements

- **Additional Providers**: Facebook, Twitter, GitHub
- **Anonymous Authentication**: Guest user support
- **Phone Authentication**: SMS-based sign-in
- **Multi-factor Authentication**: Enhanced security

## 📋 Migration Checklist

### For Developers

- [ ] Review Firebase Auth implementation
- [ ] Update UI components to use new Firebase methods
- [ ] Test authentication flows on both platforms
- [ ] Update error handling for Firebase-specific errors
- [ ] Verify email verification flow

### For QA Team

- [ ] Test Google Sign-In on Android/iOS
- [ ] Test Apple Sign-In on iOS
- [ ] Test sign-out functionality
- [ ] Test token refresh scenarios
- [ ] Test error handling (network issues, cancelled sign-in)
- [ ] Test email verification flow

### For DevOps Team

- [ ] Verify Firebase project configuration
- [ ] Monitor authentication metrics
- [ ] Set up alerts for authentication failures
- [ ] Backup Firebase service account keys

## 🔗 Related Documentation

- [Server Firebase Auth Implementation](../server/FIREBASE_AUTH_GUIDE.md)
- [BanaChef AI Design System](./UI_DESIGN_SYSTEM.md)
- [Authentication Architecture](./AUTHENTICATION_ARCHITECTURE.md)

---

**Implementation Status**: ✅ **COMPLETE AND READY FOR USE**

**Last Updated**: 2025-06-28
**Version**: 1.0.0

For questions or issues, contact the development team.
