import 'package:get_it/get_it.dart';
import 'package:dio/dio.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../api/api_client.dart';
import '../api/interceptors/auth_interceptor.dart';
import '../api/interceptors/logging_interceptor.dart';
import '../services/network_service.dart';
import '../services/storage_service.dart';
import '../services/secure_storage_service.dart';
import '../services/database_service.dart';
import '../config/app_config.dart';

// Auth imports
import '../../features/auth/services/firebase_auth_service.dart';
import '../../features/auth/services/google_auth_service.dart';
import '../../features/auth/services/apple_auth_service.dart';
import '../../features/auth/services/token_service.dart';
import '../../features/auth/repositories/auth_repository.dart';
import '../../features/auth/repositories/auth_repository_impl.dart';
import '../../features/auth/cubit/auth_cubit.dart';

// Referral imports
import '../../features/referral/services/referral_service.dart';
import '../../features/referral/cubit/referral_cubit.dart';


final GetIt getIt = GetIt.instance;

Future<void> configureDependencies() async {
  await _initializeServices();
  await _registerDependencies();
}

Future<void> _initializeServices() async {
  final databaseService = DatabaseServiceImpl();
  await databaseService.initialize();
  getIt.registerSingleton<DatabaseService>(databaseService);
}

Future<void> _registerDependencies() async {
  // Register SharedPreferences
  final sharedPreferences = await SharedPreferences.getInstance();
  getIt.registerSingleton<SharedPreferences>(sharedPreferences);

  // Register InternetConnectionChecker
  getIt.registerSingleton<InternetConnectionChecker>(
    InternetConnectionChecker.createInstance(),
  );

  // Register FlutterSecureStorage
  const secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(encryptedSharedPreferences: true),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );
  getIt.registerSingleton<FlutterSecureStorage>(secureStorage);

  // Register Dio
  final dio = Dio(
    BaseOptions(
      baseUrl: AppConfig.baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
    ),
  );

  dio.interceptors.addAll([LoggingInterceptor(), AuthInterceptor()]);

  getIt.registerSingleton<Dio>(dio);

  // Register Services
  getIt.registerSingleton<ApiClient>(ApiClient(getIt<Dio>()));
  getIt.registerSingleton<NetworkService>(
    NetworkServiceImpl(getIt<InternetConnectionChecker>()),
  );
  getIt.registerSingleton<StorageService>(
    StorageServiceImpl(getIt<SharedPreferences>()),
  );
  getIt.registerSingleton<SecureStorageService>(
    SecureStorageServiceImpl(getIt<FlutterSecureStorage>()),
  );

  // Register feature services
  _registerReferralServices();

  // Register Auth Services
  getIt.registerSingleton<FirebaseAuthService>(FirebaseAuthService());
  getIt.registerSingleton<GoogleAuthService>(GoogleAuthService());
  getIt.registerSingleton<AppleAuthService>(AppleAuthService());
  getIt.registerSingleton<TokenService>(
    TokenService(getIt<SecureStorageService>(), getIt<DatabaseService>()),
  );

  // Register Auth Repository
  getIt.registerSingleton<AuthRepository>(
    AuthRepositoryImpl(getIt<ApiClient>(), getIt<TokenService>()),
  );

  // Register Auth Cubit
  getIt.registerSingleton<AuthCubit>(
    AuthCubit(
      getIt<AuthRepository>(),
      getIt<FirebaseAuthService>(),
      getIt<GoogleAuthService>(),
      getIt<AppleAuthService>(),
    ),
  );
}

/// Register referral feature dependencies
void _registerReferralServices() {
  // Register Referral Service
  getIt.registerSingleton<ReferralService>(
    ReferralServiceImpl(getIt<ApiClient>()),
  );

  // Register Referral Cubit
  getIt.registerFactory<ReferralCubit>(
    () => ReferralCubit(getIt<ReferralService>(), getIt<TokenService>()),
  );
}
