import 'dart:io';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:injectable/injectable.dart';
import '../../../core/errors/exceptions.dart';

/// Service for handling Apple Sign-In authentication
///
/// NOTE: This is the legacy Apple Sign-In service for backward compatibility.
/// For new implementations, use FirebaseAuthService.signInWithApple() instead.
@singleton
class AppleAuthService {
  /// Sign in with Apple and return the ID token
  Future<String> signIn() async {
    try {
      // Check if Apple Sign In is available (iOS only)
      if (!Platform.isIOS) {
        throw const AuthenticationException('Apple Sign In is only available on iOS');
      }

      // Check if Apple Sign In is available on this device
      final isAvailable = await SignInWithApple.isAvailable();
      if (!isAvailable) {
        throw const AuthenticationException('Apple Sign In is not available on this device');
      }

      // Perform Apple Sign In
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
        webAuthenticationOptions: WebAuthenticationOptions(
          clientId: 'your.app.bundle.id', // TODO: Replace with actual bundle ID
          redirectUri: Uri.parse('https://your-app.com/auth/apple/callback'), // TODO: Replace with actual redirect URI
        ),
      );

      if (credential.identityToken == null) {
        throw const AuthenticationException('Failed to get Apple ID token');
      }

      return credential.identityToken!;
    } catch (e) {
      if (e is AuthenticationException) {
        rethrow;
      }
      if (e is SignInWithAppleAuthorizationException) {
        switch (e.code) {
          case AuthorizationErrorCode.canceled:
            throw const AuthenticationException('Apple sign in was cancelled');
          case AuthorizationErrorCode.failed:
            throw const AuthenticationException('Apple sign in failed');
          case AuthorizationErrorCode.invalidResponse:
            throw const AuthenticationException('Invalid response from Apple');
          case AuthorizationErrorCode.notHandled:
            throw const AuthenticationException('Apple sign in not handled');
          case AuthorizationErrorCode.unknown:
            throw const AuthenticationException('Unknown Apple sign in error');
          case AuthorizationErrorCode.notInteractive:
            throw UnimplementedError();
        }
      }
      throw AuthenticationException('Apple sign in failed: ${e.toString()}');
    }
  }

  /// Check if Apple Sign In is available
  Future<bool> isAvailable() async {
    try {
      if (!Platform.isIOS) return false;
      return await SignInWithApple.isAvailable();
    } catch (e) {
      return false;
    }
  }

  /// Get credential state for a user
  Future<CredentialState> getCredentialState(String userIdentifier) async {
    try {
      if (!Platform.isIOS) {
        throw const AuthenticationException('Apple Sign In is only available on iOS');
      }
      
      return await SignInWithApple.getCredentialState(userIdentifier);
    } catch (e) {
      throw AuthenticationException('Failed to get credential state: ${e.toString()}');
    }
  }
}
