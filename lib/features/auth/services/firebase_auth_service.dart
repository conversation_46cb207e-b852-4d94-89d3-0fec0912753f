import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:injectable/injectable.dart';
import 'dart:io';
import '../../../core/errors/exceptions.dart';
import '../../../core/config/app_config.dart';

/// Service for handling Firebase Authentication
/// Provides unified authentication through Firebase Auth for Google, Apple, and other providers
@singleton
class FirebaseAuthService {
  final FirebaseAuth _firebaseAuth;
  final GoogleSignIn _googleSignIn;

  FirebaseAuthService({
    FirebaseAuth? firebaseAuth,
    GoogleSignIn? googleSignIn,
  }) : _firebaseAuth = firebaseAuth ?? FirebaseAuth.instance,
        _googleSignIn = googleSignIn ?? GoogleSignIn(
          clientId: AppConfig.googleClientId,
        );

  /// Get current Firebase user
  User? get currentUser => _firebaseAuth.currentUser;

  /// Check if user is signed in
  bool get isSignedIn => currentUser != null;

  /// Stream of authentication state changes
  Stream<User?> get authStateChanges => _firebaseAuth.authStateChanges();

  /// Sign in with Google using Firebase Auth
  Future<String> signInWithGoogle() async {
    try {
      print('🔥 Starting Firebase Google Sign-In...');

      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      
      if (googleUser == null) {
        throw const AuthenticationException('Google sign in was cancelled');
      }

      print('🔥 Google user signed in: ${googleUser.email}');

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      if (googleAuth.accessToken == null || googleAuth.idToken == null) {
        throw const AuthenticationException('Failed to get Google authentication tokens');
      }

      print('🔥 Got Google auth tokens, creating Firebase credential...');

      // Create a new credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Sign in to Firebase with the Google credential
      final UserCredential userCredential = await _firebaseAuth.signInWithCredential(credential);
      
      if (userCredential.user == null) {
        throw const AuthenticationException('Failed to sign in with Firebase');
      }

      print('🔥 Firebase sign-in successful: ${userCredential.user!.email}');

      // Get Firebase ID token
      final String? idToken = await userCredential.user!.getIdToken();
      
      if (idToken == null) {
        throw const AuthenticationException('Failed to get Firebase ID token');
      }

      print('🔥 Firebase ID token obtained successfully');
      return idToken;

    } catch (e) {
      print('🔥 Firebase Google Sign-In error: $e');
      if (e is AuthenticationException) {
        rethrow;
      }
      throw AuthenticationException('Google sign in failed: ${e.toString()}');
    }
  }

  /// Sign in with Apple using Firebase Auth
  Future<String> signInWithApple() async {
    try {
      print('🔥 Starting Firebase Apple Sign-In...');

      // Check if Apple Sign In is available (iOS only)
      if (!Platform.isIOS) {
        throw const AuthenticationException('Apple Sign In is only available on iOS');
      }

      // Check if Apple Sign In is available on this device
      final isAvailable = await SignInWithApple.isAvailable();
      if (!isAvailable) {
        throw const AuthenticationException('Apple Sign In is not available on this device');
      }

      // Request Apple ID credential
      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      if (appleCredential.identityToken == null) {
        throw const AuthenticationException('Failed to get Apple identity token');
      }

      print('🔥 Apple credential obtained, creating Firebase credential...');

      // Create Firebase credential from Apple credential
      final oauthCredential = OAuthProvider("apple.com").credential(
        idToken: appleCredential.identityToken,
        accessToken: appleCredential.authorizationCode,
      );

      // Sign in to Firebase with the Apple credential
      final UserCredential userCredential = await _firebaseAuth.signInWithCredential(oauthCredential);
      
      if (userCredential.user == null) {
        throw const AuthenticationException('Failed to sign in with Firebase');
      }

      print('🔥 Firebase Apple sign-in successful: ${userCredential.user!.email ?? 'No email'}');

      // Get Firebase ID token
      final String? idToken = await userCredential.user!.getIdToken();
      
      if (idToken == null) {
        throw const AuthenticationException('Failed to get Firebase ID token');
      }

      print('🔥 Firebase ID token obtained successfully');
      return idToken;

    } catch (e) {
      print('🔥 Firebase Apple Sign-In error: $e');
      if (e is AuthenticationException) {
        rethrow;
      }
      throw AuthenticationException('Apple sign in failed: ${e.toString()}');
    }
  }

  /// Sign out from Firebase and all providers
  Future<void> signOut() async {
    try {
      print('🔥 Signing out from Firebase...');
      
      // Sign out from Google
      await _googleSignIn.signOut();
      
      // Sign out from Firebase
      await _firebaseAuth.signOut();
      
      print('🔥 Successfully signed out from all providers');
    } catch (e) {
      print('🔥 Sign out error: $e');
      throw AuthenticationException('Sign out failed: ${e.toString()}');
    }
  }

  /// Get current user's Firebase ID token
  Future<String?> getCurrentUserIdToken() async {
    try {
      final user = currentUser;
      if (user == null) return null;
      
      return await user.getIdToken();
    } catch (e) {
      print('🔥 Error getting current user ID token: $e');
      return null;
    }
  }

  /// Refresh current user's Firebase ID token
  Future<String?> refreshIdToken() async {
    try {
      final user = currentUser;
      if (user == null) return null;
      
      return await user.getIdToken(true); // Force refresh
    } catch (e) {
      print('🔥 Error refreshing ID token: $e');
      return null;
    }
  }

  /// Check if current user's email is verified
  bool get isEmailVerified => currentUser?.emailVerified ?? false;

  /// Send email verification to current user
  Future<void> sendEmailVerification() async {
    try {
      final user = currentUser;
      if (user == null) {
        throw const AuthenticationException('No user signed in');
      }
      
      if (user.emailVerified) {
        print('🔥 Email already verified');
        return;
      }
      
      await user.sendEmailVerification();
      print('🔥 Email verification sent');
    } catch (e) {
      print('🔥 Error sending email verification: $e');
      throw AuthenticationException('Failed to send email verification: ${e.toString()}');
    }
  }

  /// Reload current user data
  Future<void> reloadUser() async {
    try {
      final user = currentUser;
      if (user == null) return;
      
      await user.reload();
    } catch (e) {
      print('🔥 Error reloading user: $e');
    }
  }
}
