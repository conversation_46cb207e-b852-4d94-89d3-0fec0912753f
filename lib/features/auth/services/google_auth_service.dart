import 'package:banachef/core/config/app_config.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:injectable/injectable.dart';
import '../../../core/errors/exceptions.dart';

/// Service for handling Google Sign-In authentication
///
/// NOTE: This is the legacy Google Sign-In service for backward compatibility.
/// For new implementations, use FirebaseAuthService.signInWithGoogle() instead.
@singleton
class GoogleAuthService {
  late final GoogleSignIn _googleSignIn;

  GoogleAuthService() {
    _googleSignIn = GoogleSignIn(
      scopes: [
        'email',
        'profile',
      ],
      serverClientId: AppConfig.googleClientId,
    );
  }

  /// Sign in with Google and return the ID token
  Future<String> signIn() async {
    try {
      print('🔐 Starting Google Sign-In process...');

      // Check if user is already signed in
      GoogleSignInAccount? account = _googleSignIn.currentUser;
      print('🔐 Current user: ${account?.email ?? 'None'}');

      // If not signed in, trigger sign in flow
      if (account == null) {
        print('🔐 No current user, starting sign-in flow...');
        account = await _googleSignIn.signIn();
      }

      if (account == null) {
        print('🔐 Sign-in was cancelled by user');
        throw const AuthenticationException('Google sign in was cancelled');
      }

      print('🔐 Sign-in successful for: ${account.email}');
      print('🔐 Getting authentication details...');

      // Get authentication details
      final GoogleSignInAuthentication auth = await account.authentication;

      print('🔐 Access token available: ${auth.accessToken != null}');
      print('🔐 ID token available: ${auth.idToken != null}');

      if (auth.idToken == null) {
        print('🔐 ERROR: ID token is null');
        print('🔐 Access token: ${auth.accessToken?.substring(0, 20)}...');
        throw const AuthenticationException('Failed to get Google ID token. This might be due to missing OAuth configuration.');
      }

      print('🔐 ID token obtained successfully');
      return auth.idToken!;
    } catch (e) {
      print('🔐 ERROR in Google Sign-In: $e');
      if (e is AuthenticationException) {
        rethrow;
      }
      throw AuthenticationException('Google sign in failed: ${e.toString()}');
    }
  }

  /// Sign out from Google
  Future<void> signOut() async {
    try {
      await _googleSignIn.signOut();
    } catch (e) {
      throw AuthenticationException('Google sign out failed: ${e.toString()}');
    }
  }

  /// Check if user is currently signed in
  Future<bool> isSignedIn() async {
    try {
      return await _googleSignIn.isSignedIn();
    } catch (e) {
      return false;
    }
  }

  /// Get current user info (if signed in)
  Future<GoogleSignInAccount?> getCurrentUser() async {
    try {
      return _googleSignIn.currentUser ?? await _googleSignIn.signInSilently();
    } catch (e) {
      return null;
    }
  }

  /// Disconnect from Google (revoke access)
  Future<void> disconnect() async {
    try {
      await _googleSignIn.disconnect();
    } catch (e) {
      throw AuthenticationException('Google disconnect failed: ${e.toString()}');
    }
  }
}
