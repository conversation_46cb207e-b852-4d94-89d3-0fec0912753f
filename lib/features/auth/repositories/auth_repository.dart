import '../models/auth_response.dart';
import '../models/auth_user.dart';

/// Abstract repository for authentication operations
abstract class AuthRepository {
  /// Authenticate with Firebase ID token (new primary method)
  Future<AuthResponse> loginWithFirebase(String firebaseToken);

  /// Authenticate with Google ID token (legacy support)
  Future<AuthResponse> loginWithGoogle(String idToken);

  /// Authenticate with Apple ID token (legacy support)
  Future<AuthResponse> loginWithApple(String idToken);

  /// Refresh access token using refresh token
  Future<AuthResponse> refreshToken(String refreshToken);

  /// Logout user (clear tokens)
  Future<void> logout();

  /// Check if user is currently authenticated
  Future<bool> isAuthenticated();

  /// Get current user data
  Future<AuthUser?> getCurrentUser();

  /// Clear all authentication data
  Future<void> clearAuthData();
}
