import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'auth_request.g.dart';

/// Base class for authentication requests
abstract class AuthRequest extends Equatable {
  const AuthRequest();
}

/// Google authentication request
@JsonSerializable()
class GoogleAuthRequest extends AuthRequest {
  @JsonKey(name: 'token')
  final String idToken;

  const GoogleAuthRequest({
    required this.idToken,
  });

  factory GoogleAuthRequest.fromJson(Map<String, dynamic> json) =>
      _$GoogleAuthRequestFromJson(json);

  Map<String, dynamic> toJson() => _$GoogleAuthRequestToJson(this);

  @override
  List<Object?> get props => [idToken];
}

/// Apple authentication request
@JsonSerializable()
class AppleAuthRequest extends AuthRequest {
  @JsonKey(name: 'token')
  final String idToken;

  const AppleAuthRequest({
    required this.idToken,
  });

  factory AppleAuthRequest.fromJson(Map<String, dynamic> json) =>
      _$AppleAuthRequestFromJson(json);

  Map<String, dynamic> toJson() => _$AppleAuthRequestToJson(this);

  @override
  List<Object?> get props => [idToken];
}

/// Firebase authentication request
@JsonSerializable()
class FirebaseAuthRequest extends AuthRequest {
  @JsonKey(name: 'firebase_token')
  final String firebaseToken;

  const FirebaseAuthRequest({
    required this.firebaseToken,
  });

  factory FirebaseAuthRequest.fromJson(Map<String, dynamic> json) =>
      _$FirebaseAuthRequestFromJson(json);

  Map<String, dynamic> toJson() => _$FirebaseAuthRequestToJson(this);

  @override
  List<Object?> get props => [firebaseToken];
}
