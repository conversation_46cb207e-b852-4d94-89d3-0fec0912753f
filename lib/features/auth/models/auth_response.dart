import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'auth_user.dart';

part 'auth_response.g.dart';

/// Authentication response from server
@JsonSerializable()
class AuthResponse extends Equatable {
  @Json<PERSON>ey(name: 'access_token')
  final String accessToken;

  @Json<PERSON>ey(name: 'refresh_token')
  final String refreshToken;

  @Json<PERSON>ey(name: 'token_type')
  final String tokenType;

  @J<PERSON><PERSON>ey(name: 'user')
  final AuthUser user;

  @Json<PERSON>ey(name: 'referral_code')
  final String? referralCode;

  const AuthResponse({
    required this.accessToken,
    required this.refreshToken,
    required this.tokenType,
    required this.user,
    this.referralCode,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) =>
      _$AuthResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AuthResponseToJson(this);

  @override
  List<Object?> get props => [
        accessToken,
        refreshToken,
        tokenType,
        user,
        referralCode,
      ];
}

/// Error response from authentication
@JsonSerializable()
class AuthErrorResponse extends Equatable {
  @JsonKey(name: 'detail')
  final String detail;

  const AuthErrorResponse({
    required this.detail,
  });

  factory AuthErrorResponse.fromJson(Map<String, dynamic> json) =>
      _$AuthErrorResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AuthErrorResponseToJson(this);

  @override
  List<Object?> get props => [detail];
}
