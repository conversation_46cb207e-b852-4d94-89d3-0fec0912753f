import '../../../ui/kit/ui_kit.dart';
import '../models/onboarding_constants.dart';

/// Reusable chip selector widget for onboarding
/// Used for allergies, food preferences, etc.
class ChipSelector extends StatefulWidget {
  /// List of available options
  final List<String> options;
  
  /// Currently selected values
  final List<String> selectedValues;
  
  /// Callback when selection changes
  final void Function(List<String> values) onSelectionChanged;
  
  /// Whether to show "Add custom" option
  final bool allowCustom;
  
  /// Placeholder text for custom input
  final String? customPlaceholder;
  
  /// Whether "Không có" option should deselect others
  final bool hasNoneOption;
  
  /// The value that represents "none" (usually "Không có")
  final String? noneValue;

  const ChipSelector({
    super.key,
    required this.options,
    required this.selectedValues,
    required this.onSelectionChanged,
    this.allowCustom = true,
    this.customPlaceholder,
    this.hasNoneOption = true,
    this.noneValue = 'Không có',
  });

  @override
  State<ChipSelector> createState() => _ChipSelectorState();
}

class _ChipSelectorState extends State<ChipSelector> {
  final TextEditingController _customController = TextEditingController();
  bool _showCustomInput = false;
  List<String> _customOptions = [];

  @override
  void dispose() {
    _customController.dispose();
    super.dispose();
  }

  void _handleSelection(String value) {
    final currentValues = List<String>.from(widget.selectedValues);
    
    if (widget.hasNoneOption && value == widget.noneValue) {
      // If selecting "none", clear all other selections
      if (currentValues.contains(value)) {
        currentValues.remove(value);
      } else {
        currentValues.clear();
        currentValues.add(value);
      }
    } else {
      // If selecting something other than "none", remove "none" if present
      if (widget.hasNoneOption && currentValues.contains(widget.noneValue)) {
        currentValues.remove(widget.noneValue);
      }
      
      // Toggle the selected value
      if (currentValues.contains(value)) {
        currentValues.remove(value);
      } else {
        currentValues.add(value);
      }
    }
    
    widget.onSelectionChanged(currentValues);
  }

  void _addCustomOption() {
    final customValue = _customController.text.trim();
    if (customValue.isNotEmpty && !_allOptions.contains(customValue)) {
      setState(() {
        _customOptions.add(customValue);
        _showCustomInput = false;
        _customController.clear();
      });
      
      // Auto-select the newly added option
      _handleSelection(customValue);
    }
  }

  List<String> get _allOptions => [...widget.options, ..._customOptions];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Chips
        Wrap(
          spacing: context.spacingSM,
          runSpacing: context.spacingSM,
          children: [
            // Regular options
            ..._allOptions.map((option) => _buildChip(option, theme)),
            
            // Add custom button
            if (widget.allowCustom && !_showCustomInput)
              _buildAddCustomChip(theme),
          ],
        ),
        
        // Custom input field
        if (_showCustomInput) ...[
          VSpace.md(),
          Row(
            children: [
              Expanded(
                child: BanaTextField(
                  controller: _customController,
                  hint: widget.customPlaceholder ?? 'Nhập tùy chọn...',
                  onSubmitted: (_) => _addCustomOption(),
                ),
              ),
              BanaSpacing.horizontalSpacing.sm,
              BanaIconButton.filled(
                icon: Icons.add,
                onPressed: _addCustomOption,
                backgroundColor: BanaColors.primary,
                iconColor: BanaColors.onPrimary,
                accessibilityLabel: 'Thêm tùy chọn',
              ),
              BanaSpacing.horizontalSpacing.xs,
              BanaIconButton(
                icon: Icons.close,
                onPressed: () {
                  setState(() {
                    _showCustomInput = false;
                    _customController.clear();
                  });
                },
                accessibilityLabel: 'Đóng',
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildChip(String option, ThemeData theme) {
    final isSelected = widget.selectedValues.contains(option);
    final isCustom = _customOptions.contains(option);
    
    return GestureDetector(
      onTap: () => _handleSelection(option),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: EdgeInsets.symmetric(
          horizontal: context.spacingMD,
          vertical: context.spacingSM,
        ),
        decoration: BoxDecoration(
          color: isSelected 
              ? const Color(OnboardingConstants.completedColor)
              : theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected
                ? const Color(OnboardingConstants.completedColor)
                : theme.colorScheme.outline.withValues(alpha: 0.3),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: theme.shadowColor.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              option,
              style: context.bodyMedium.copyWith(
                color: isSelected 
                    ? Colors.white
                    : theme.colorScheme.onSurface,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
            ),
            
            // Remove button for custom options
            if (isCustom && isSelected) ...[
              HSpace.xs(),
              GestureDetector(
                onTap: () {
                  setState(() {
                    _customOptions.remove(option);
                  });
                  _handleSelection(option); // This will remove it from selection
                },
                child: Icon(
                  Icons.close,
                  size: 16,
                  color: Colors.white,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAddCustomChip(ThemeData theme) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _showCustomInput = true;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: context.spacingMD,
          vertical: context.spacingSM,
        ),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: theme.colorScheme.outline.withValues(alpha: 0.4), // Nhạt hơn như placeholder
            width: 1.0, // Mỏng hơn
            style: BorderStyle.solid,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.add,
              size: 14, // Nhỏ hơn
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5), // Nhạt như placeholder
            ),
            HSpace.xs(),
            Text(
              'Thêm tùy chọn',
              style: context.bodySmall.copyWith( // Nhỏ hơn từ bodyMedium → bodySmall
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5), // Nhạt như placeholder
                fontWeight: FontWeight.w400, // Nhẹ hơn
                fontSize: 13, // Font size nhỏ hơn
              ),
            ),
          ],
        ),
      ),
    );
  }
}
