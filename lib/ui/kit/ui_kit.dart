/// BanaChef AI UI Kit
/// 
/// Comprehensive UI component library following BanaChef Design System
/// 
/// This is the main export file for the UI Kit. Import this file to get
/// access to all UI components, design tokens, and utilities.
/// 
/// Usage:
/// ```dart
/// import 'package:banachef/ui/kit/ui_kit.dart';
/// 
/// // Use any component
/// BanaButton.primary(
///   text: 'Get Started',
///   onPressed: () {},
/// )
/// 
/// // Use design tokens
/// Container(
///   color: BanaColors.primary,
///   padding: BanaSpacing.lg,
/// )
/// ```
library ui_kit;

// Design Tokens
export 'tokens/bana_colors.dart';
export 'tokens/bana_typography.dart';
export 'tokens/bana_spacing.dart';
export 'tokens/bana_shadows.dart';
export 'tokens/bana_borders.dart';

// Core Components
export 'components/buttons/bana_button.dart';
export 'components/buttons/bana_icon_button.dart';
export 'components/buttons/bana_fab.dart';

export 'components/cards/bana_recipe_card.dart';
export 'components/cards/bana_category_card.dart';
export 'components/cards/bana_pantry_card.dart';

export 'components/inputs/bana_text_field.dart';
export 'components/inputs/bana_search_bar.dart';
export 'components/inputs/bana_segmented_control.dart';
export 'components/inputs/bana_selection_controls.dart';

export 'components/overlays/bana_modal.dart';
export 'components/overlays/bana_bottom_sheet.dart';
export 'components/overlays/bana_toast.dart';

export 'components/icons/bana_icons.dart';


// Examples
export 'examples/ui_kit_showcase.dart';

// Re-export commonly used Flutter widgets
export 'package:flutter/material.dart';

// Re-export responsive system
export '../responsive/responsive.dart';
