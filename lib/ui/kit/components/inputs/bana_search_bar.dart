import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../tokens/bana_colors.dart';
import '../../tokens/bana_typography.dart';
import '../../tokens/bana_spacing.dart';
import '../../tokens/bana_borders.dart';
import '../buttons/bana_icon_button.dart';

/// BanaChef AI Search Bar Component
/// 
/// Search input component following the BanaChef Design System.
/// Optimized for search functionality with suggestions and filters.
/// 
/// Usage:
/// ```dart
/// BanaSearchBar(
///   hint: 'Search recipes...',
///   onChanged: (query) => performSearch(query),
///   onSubmitted: (query) => navigateToResults(query),
///   suggestions: ['Pasta', 'Pizza', 'Salad'],
///   onSuggestionTap: (suggestion) => selectSuggestion(suggestion),
/// )
/// 
/// // With filter button
/// BanaSearchBar.withFilter(
///   hint: 'Search ingredients...',
///   onFilterTap: () => showFilterDialog(),
/// )
/// ```
class BanaSearchBar extends StatefulWidget {
  /// Search hint text
  final String hint;
  
  /// Initial search query
  final String? initialQuery;
  
  /// Text controller
  final TextEditingController? controller;
  
  /// Whether search bar is enabled
  final bool enabled;
  
  /// Whether to show filter button
  final bool showFilter;
  
  /// Whether to show clear button
  final bool showClear;
  
  /// Whether to auto focus
  final bool autofocus;
  
  /// Callback when search query changes
  final ValueChanged<String>? onChanged;
  
  /// Callback when search is submitted
  final ValueChanged<String>? onSubmitted;
  
  /// Callback when search bar is tapped
  final VoidCallback? onTap;
  
  /// Callback when filter button is tapped
  final VoidCallback? onFilterTap;
  
  /// Callback when clear button is tapped
  final VoidCallback? onClear;
  
  /// Search suggestions
  final List<String>? suggestions;
  
  /// Callback when suggestion is tapped
  final ValueChanged<String>? onSuggestionTap;
  
  /// Focus node
  final FocusNode? focusNode;
  
  /// Search bar variant
  final BanaSearchBarVariant variant;

  const BanaSearchBar({
    super.key,
    this.hint = 'Search...',
    this.initialQuery,
    this.controller,
    this.enabled = true,
    this.showFilter = false,
    this.showClear = true,
    this.autofocus = false,
    this.onChanged,
    this.onSubmitted,
    this.onTap,
    this.onFilterTap,
    this.onClear,
    this.suggestions,
    this.onSuggestionTap,
    this.focusNode,
    this.variant = BanaSearchBarVariant.standard,
  });

  /// Search bar with filter button constructor
  const BanaSearchBar.withFilter({
    super.key,
    this.hint = 'Search...',
    this.initialQuery,
    this.controller,
    this.enabled = true,
    this.showClear = true,
    this.autofocus = false,
    this.onChanged,
    this.onSubmitted,
    this.onTap,
    required this.onFilterTap,
    this.onClear,
    this.suggestions,
    this.onSuggestionTap,
    this.focusNode,
    this.variant = BanaSearchBarVariant.standard,
  }) : showFilter = true;

  @override
  State<BanaSearchBar> createState() => _BanaSearchBarState();
}

class _BanaSearchBarState extends State<BanaSearchBar> {
  late FocusNode _focusNode;
  late TextEditingController _controller;
  bool _showSuggestions = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _controller = widget.controller ?? TextEditingController(text: widget.initialQuery);
    
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  void _onFocusChanged() {
    setState(() {
      _showSuggestions = _focusNode.hasFocus && 
          widget.suggestions != null && 
          widget.suggestions!.isNotEmpty;
    });
  }

  void _onChanged(String value) {
    setState(() {
      _showSuggestions = value.isNotEmpty && 
          _focusNode.hasFocus && 
          widget.suggestions != null && 
          widget.suggestions!.isNotEmpty;
    });
    
    widget.onChanged?.call(value);
  }

  void _onSubmitted(String value) {
    _hideSuggestions();
    widget.onSubmitted?.call(value);
  }

  void _onClear() {
    _controller.clear();
    _hideSuggestions();
    widget.onClear?.call();
    widget.onChanged?.call('');
  }

  void _onSuggestionTap(String suggestion) {
    _controller.text = suggestion;
    _hideSuggestions();
    _focusNode.unfocus();
    widget.onSuggestionTap?.call(suggestion);
  }

  void _hideSuggestions() {
    setState(() => _showSuggestions = false);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildSearchField(),
        if (_showSuggestions) _buildSuggestions(),
      ],
    );
  }

  Widget _buildSearchField() {
    final isFocused = _focusNode.hasFocus;
    
    return Container(
      decoration: BoxDecoration(
        color: BanaColors.surface,
        borderRadius: _getBorderRadius(),
        border: Border.all(
          color: isFocused ? BanaColors.primary : BanaColors.border,
          width: isFocused ? BanaBorders.widthThick : BanaBorders.widthThin,
        ),
        boxShadow: isFocused ? [
          BoxShadow(
            color: BanaColors.primary.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ] : null,
      ),
      child: Row(
        children: [
          // Search icon
          Padding(
            padding: BanaSpacing.horizontal.md,
            child: Icon(
              Icons.search,
              color: isFocused ? BanaColors.primary : BanaColors.textSecondary,
              size: 20,
            ),
          ),
          
          // Text field
          Expanded(
            child: TextField(
              controller: _controller,
              focusNode: _focusNode,
              enabled: widget.enabled,
              autofocus: widget.autofocus,
              onChanged: _onChanged,
              onSubmitted: _onSubmitted,
              onTap: widget.onTap,
              style: BanaTypography.bodyLarge.copyWith(
                color: widget.enabled ? BanaColors.text : BanaColors.onDisabled,
              ),
              decoration: InputDecoration(
                hintText: widget.hint,
                hintStyle: BanaTypography.bodyLarge.copyWith(
                  color: BanaColors.textTertiary,
                ),
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(
                  vertical: BanaSpacing.md,
                ),
              ),
            ),
          ),
          
          // Action buttons
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Clear button
              if (widget.showClear && _controller.text.isNotEmpty)
                BanaIconButton(
                  icon: Icons.clear,
                  onPressed: _onClear,
                  size: BanaIconButtonSize.small,
                  accessibilityLabel: 'Clear search',
                ),
              
              // Filter button
              if (widget.showFilter)
                BanaIconButton(
                  icon: Icons.tune,
                  onPressed: widget.onFilterTap,
                  size: BanaIconButtonSize.small,
                  accessibilityLabel: 'Filter results',
                ),
              
              BanaSpacing.horizontalSpacing.xs,
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSuggestions() {
    if (widget.suggestions == null || widget.suggestions!.isEmpty) {
      return const SizedBox.shrink();
    }

    final filteredSuggestions = widget.suggestions!
        .where((suggestion) => 
            suggestion.toLowerCase().contains(_controller.text.toLowerCase()))
        .take(5)
        .toList();

    if (filteredSuggestions.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: EdgeInsets.only(top: BanaSpacing.xs),
      decoration: BoxDecoration(
        color: BanaColors.surface,
        borderRadius: _getBorderRadius(),
        border: Border.all(color: BanaColors.border),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: filteredSuggestions.asMap().entries.map((entry) {
          final index = entry.key;
          final suggestion = entry.value;
          final isLast = index == filteredSuggestions.length - 1;
          
          return _buildSuggestionItem(suggestion, isLast);
        }).toList(),
      ),
    );
  }

  Widget _buildSuggestionItem(String suggestion, bool isLast) {
    return InkWell(
      onTap: () => _onSuggestionTap(suggestion),
      borderRadius: isLast 
          ? _getBorderRadius()
          : const BorderRadius.only(
              topLeft: Radius.circular(BanaBorders.radiusMD),
              topRight: Radius.circular(BanaBorders.radiusMD),
            ),
      child: Container(
        width: double.infinity,
        padding: BanaSpacing.all.md,
        decoration: BoxDecoration(
          border: isLast ? null : Border(
            bottom: BorderSide(
              color: BanaColors.borderLight,
              width: BanaBorders.widthThin,
            ),
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.search,
              size: 16,
              color: BanaColors.textSecondary,
            ),
            BanaSpacing.horizontalSpacing.sm,
            Expanded(
              child: Text(
                suggestion,
                style: BanaTypography.bodyMedium,
              ),
            ),
            Icon(
              Icons.north_west,
              size: 16,
              color: BanaColors.textSecondary,
            ),
          ],
        ),
      ),
    );
  }

  BorderRadius _getBorderRadius() {
    switch (widget.variant) {
      case BanaSearchBarVariant.standard:
        return BanaBorders.radius.md;
      case BanaSearchBarVariant.rounded:
        return BanaBorders.radius.circular;
    }
  }
}

/// Search bar variant types
enum BanaSearchBarVariant {
  /// Standard search bar with medium border radius
  standard,
  
  /// Rounded search bar with circular border radius
  rounded,
}
