import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../tokens/bana_colors.dart';
import '../../tokens/bana_shadows.dart';
import '../../tokens/bana_borders.dart';

/// BanaChef AI Floating Action Button Component
/// 
/// Floating Action Button component following the BanaChef Design System.
/// Designed for primary creative actions with expandable menu support.
/// 
/// Usage:
/// ```dart
/// // Simple FAB
/// BanaFAB(
///   icon: Icons.add,
///   onPressed: () {},
///   accessibilityLabel: 'Add new recipe',
/// )
/// 
/// // FAB with expandable menu
/// BanaFAB.expandable(
///   icon: Icons.add,
///   accessibilityLabel: 'Add content',
///   actions: [
///     BanaFABAction(
///       icon: Icons.camera_alt,
///       label: 'Scan Recipe',
///       onPressed: () {},
///     ),
///     BanaFABAction(
///       icon: Icons.edit,
///       label: 'Create Recipe',
///       onPressed: () {},
///     ),
///   ],
/// )
/// ```
class BanaFAB extends StatefulWidget {
  /// Icon to display
  final IconData icon;
  
  /// Callback when FAB is pressed
  final VoidCallback? onPressed;
  
  /// FAB size variant
  final BanaFABSize size;
  
  /// Background color (overrides theme)
  final Color? backgroundColor;
  
  /// Icon color (overrides theme)
  final Color? iconColor;
  
  /// Accessibility label for screen readers
  final String accessibilityLabel;
  
  /// Optional tooltip text
  final String? tooltip;
  
  /// Expandable actions (if provided, FAB becomes expandable)
  final List<BanaFABAction>? actions;
  
  /// Whether FAB is currently expanded (for expandable FAB)
  final bool isExpanded;
  
  /// Callback when expansion state changes
  final ValueChanged<bool>? onExpansionChanged;

  const BanaFAB({
    super.key,
    required this.icon,
    this.onPressed,
    this.size = BanaFABSize.regular,
    this.backgroundColor,
    this.iconColor,
    required this.accessibilityLabel,
    this.tooltip,
    this.actions,
    this.isExpanded = false,
    this.onExpansionChanged,
  });

  /// Expandable FAB constructor
  const BanaFAB.expandable({
    super.key,
    required this.icon,
    this.size = BanaFABSize.regular,
    this.backgroundColor,
    this.iconColor,
    required this.accessibilityLabel,
    this.tooltip,
    required this.actions,
    this.isExpanded = false,
    this.onExpansionChanged,
  }) : onPressed = null;

  @override
  State<BanaFAB> createState() => _BanaFABState();
}

class _BanaFABState extends State<BanaFAB>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _rotationController;
  late AnimationController _expansionController;
  
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _expansionAnimation;
  
  bool _isPressed = false;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _expansionController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.9,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));
    
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.125, // 45 degrees (1/8 turn)
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.easeInOut,
    ));
    
    _expansionAnimation = CurvedAnimation(
      parent: _expansionController,
      curve: Curves.easeOutBack,
    );
    
    _isExpanded = widget.isExpanded;
    if (_isExpanded) {
      _expansionController.value = 1.0;
      _rotationController.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(BanaFAB oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isExpanded != _isExpanded) {
      _toggleExpansion(widget.isExpanded);
    }
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _rotationController.dispose();
    _expansionController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    setState(() => _isPressed = true);
    _scaleController.forward();
    HapticFeedback.lightImpact();
  }

  void _onTapUp(TapUpDetails details) {
    _resetPressedState();
  }

  void _onTapCancel() {
    _resetPressedState();
  }

  void _resetPressedState() {
    if (_isPressed) {
      setState(() => _isPressed = false);
      _scaleController.reverse();
    }
  }

  void _onTap() {
    if (widget.actions != null && widget.actions!.isNotEmpty) {
      _toggleExpansion(!_isExpanded);
    } else if (widget.onPressed != null) {
      widget.onPressed!();
    }
  }

  void _toggleExpansion(bool expand) {
    setState(() => _isExpanded = expand);
    
    if (expand) {
      _expansionController.forward();
      _rotationController.forward();
    } else {
      _expansionController.reverse();
      _rotationController.reverse();
    }
    
    widget.onExpansionChanged?.call(expand);
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        // Expandable actions
        if (widget.actions != null && widget.actions!.isNotEmpty)
          ..._buildExpandableActions(),
        
        // Main FAB
        _buildMainFAB(),
      ],
    );
  }

  List<Widget> _buildExpandableActions() {
    return widget.actions!.asMap().entries.map((entry) {
      final index = entry.key;
      final action = entry.value;
      
      return AnimatedBuilder(
        animation: _expansionAnimation,
        builder: (context, child) {
          final offset = (index + 1) * 72.0 * _expansionAnimation.value;
          
          return Positioned(
            bottom: offset,
            right: 0,
            child: Transform.scale(
              scale: _expansionAnimation.value,
              child: Opacity(
                opacity: _expansionAnimation.value,
                child: _buildActionButton(action),
              ),
            ),
          );
        },
      );
    }).toList();
  }

  Widget _buildActionButton(BanaFABAction action) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Label
        if (action.label != null)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: BanaColors.surface,
              borderRadius: BorderRadius.circular(16),
              boxShadow: BanaShadows.elevation2,
            ),
            child: Text(
              action.label!,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: BanaColors.text,
              ),
            ),
          ),
        
        if (action.label != null) const SizedBox(width: 12),
        
        // Action button
        GestureDetector(
          onTap: () {
            action.onPressed();
            _toggleExpansion(false);
          },
          child: Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: action.backgroundColor ?? BanaColors.surface,
              shape: BoxShape.circle,
              boxShadow: BanaShadows.elevation2,
            ),
            child: Icon(
              action.icon,
              size: 24,
              color: action.iconColor ?? BanaColors.text,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMainFAB() {
    final fabSize = _getFABSize();
    final iconSize = _getIconSize();
    
    return AnimatedBuilder(
      animation: Listenable.merge([_scaleAnimation, _rotationAnimation]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value * 2 * 3.14159, // Convert to radians
            child: GestureDetector(
              onTapDown: _onTapDown,
              onTapUp: _onTapUp,
              onTapCancel: _onTapCancel,
              onTap: _onTap,
              child: Container(
                width: fabSize,
                height: fabSize,
                decoration: BoxDecoration(
                  color: widget.backgroundColor ?? BanaColors.text,
                  shape: BoxShape.circle,
                  boxShadow: BanaShadows.fab,
                ),
                child: Icon(
                  widget.icon,
                  size: iconSize,
                  color: widget.iconColor ?? BanaColors.surface,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  double _getFABSize() {
    switch (widget.size) {
      case BanaFABSize.small:
        return 48.0;
      case BanaFABSize.regular:
        return 56.0;
      case BanaFABSize.large:
        return 64.0;
    }
  }

  double _getIconSize() {
    switch (widget.size) {
      case BanaFABSize.small:
        return 20.0;
      case BanaFABSize.regular:
        return 24.0;
      case BanaFABSize.large:
        return 28.0;
    }
  }
}

/// FAB Action for expandable FAB
class BanaFABAction {
  /// Icon to display
  final IconData icon;
  
  /// Optional label text
  final String? label;
  
  /// Callback when action is pressed
  final VoidCallback onPressed;
  
  /// Background color (overrides theme)
  final Color? backgroundColor;
  
  /// Icon color (overrides theme)
  final Color? iconColor;

  const BanaFABAction({
    required this.icon,
    this.label,
    required this.onPressed,
    this.backgroundColor,
    this.iconColor,
  });
}

/// FAB size variants
enum BanaFABSize {
  /// Small FAB - 48pt diameter
  small,
  
  /// Regular FAB - 56pt diameter
  regular,
  
  /// Large FAB - 64pt diameter
  large,
}
