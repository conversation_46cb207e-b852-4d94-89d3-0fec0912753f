import 'package:flutter/material.dart';
import '../../tokens/bana_colors.dart';

/// BanaChef AI Icon System
/// 
/// Comprehensive icon system following the BanaChef Design System.
/// Provides consistent icon styling and accessibility features.
/// 
/// Usage:
/// ```dart
/// // Standard icon
/// BanaIcon(
///   Icons.favorite,
///   size: BanaIconSize.medium,
///   color: BanaColors.primary,
/// )
/// 
/// // Semantic icons
/// BanaIcon.navigation(Icons.home)
/// BanaIcon.action(Icons.add)
/// BanaIcon.status(Icons.check_circle, color: BanaColors.success)
/// ```
class BanaIcon extends StatelessWidget {
  /// Icon data
  final IconData icon;
  
  /// Icon size
  final BanaIconSize size;
  
  /// Icon color
  final Color? color;
  
  /// Icon semantic type
  final BanaIconType type;
  
  /// Custom size override
  final double? customSize;
  
  /// Accessibility label
  final String? semanticLabel;

  const BanaIcon(
    this.icon, {
    super.key,
    this.size = BanaIconSize.medium,
    this.color,
    this.type = BanaIconType.general,
    this.customSize,
    this.semanticLabel,
  });

  /// Navigation icon constructor
  const BanaIcon.navigation(
    this.icon, {
    super.key,
    this.size = BanaIconSize.medium,
    this.color,
    this.customSize,
    this.semanticLabel,
  }) : type = BanaIconType.navigation;

  /// Action icon constructor
  const BanaIcon.action(
    this.icon, {
    super.key,
    this.size = BanaIconSize.medium,
    this.color,
    this.customSize,
    this.semanticLabel,
  }) : type = BanaIconType.action;

  /// Status icon constructor
  const BanaIcon.status(
    this.icon, {
    super.key,
    this.size = BanaIconSize.medium,
    this.color,
    this.customSize,
    this.semanticLabel,
  }) : type = BanaIconType.status;

  /// Information icon constructor
  const BanaIcon.info(
    this.icon, {
    super.key,
    this.size = BanaIconSize.medium,
    this.color,
    this.customSize,
    this.semanticLabel,
  }) : type = BanaIconType.information;

  @override
  Widget build(BuildContext context) {
    final iconSize = customSize ?? _getIconSize();
    final iconColor = color ?? _getDefaultColor();
    
    Widget iconWidget = Icon(
      icon,
      size: iconSize,
      color: iconColor,
      semanticLabel: semanticLabel,
    );
    
    // Add semantic wrapper if needed
    if (semanticLabel != null) {
      iconWidget = Semantics(
        label: semanticLabel,
        child: iconWidget,
      );
    }
    
    return iconWidget;
  }

  double _getIconSize() {
    switch (size) {
      case BanaIconSize.tiny:
        return 12.0;
      case BanaIconSize.small:
        return 16.0;
      case BanaIconSize.medium:
        return 24.0;
      case BanaIconSize.large:
        return 32.0;
      case BanaIconSize.extraLarge:
        return 48.0;
    }
  }

  Color _getDefaultColor() {
    switch (type) {
      case BanaIconType.navigation:
        return BanaColors.text;
      case BanaIconType.action:
        return BanaColors.primary;
      case BanaIconType.status:
        return BanaColors.success;
      case BanaIconType.information:
        return BanaColors.info;
      case BanaIconType.general:
        return BanaColors.textSecondary;
    }
  }
}

/// Icon size variants
enum BanaIconSize {
  /// Tiny icon - 12pt
  tiny,
  
  /// Small icon - 16pt
  small,
  
  /// Medium icon - 24pt (default)
  medium,
  
  /// Large icon - 32pt
  large,
  
  /// Extra large icon - 48pt
  extraLarge,
}

/// Icon semantic types
enum BanaIconType {
  /// General purpose icons
  general,
  
  /// Navigation icons (home, back, menu)
  navigation,
  
  /// Action icons (add, edit, delete)
  action,
  
  /// Status icons (success, error, warning)
  status,
  
  /// Information icons (info, help)
  information,
}

/// BanaChef specific icon collection
class BanaIcons {
  BanaIcons._(); // Private constructor

  // ============================================================================
  // NAVIGATION ICONS
  // ============================================================================
  
  /// Home icon
  static const IconData home = Icons.home;
  
  /// Explore/Discovery icon
  static const IconData explore = Icons.explore;
  
  /// Meal plan icon
  static const IconData mealPlan = Icons.calendar_today;
  
  /// Pantry icon
  static const IconData pantry = Icons.inventory_2;
  
  /// Profile icon
  static const IconData profile = Icons.person;
  
  /// Back navigation icon
  static const IconData back = Icons.arrow_back;
  
  /// Menu icon
  static const IconData menu = Icons.menu;
  
  /// Close icon
  static const IconData close = Icons.close;

  // ============================================================================
  // ACTION ICONS
  // ============================================================================
  
  /// Add/Plus icon
  static const IconData add = Icons.add;
  
  /// Edit icon
  static const IconData edit = Icons.edit;
  
  /// Delete icon
  static const IconData delete = Icons.delete;
  
  /// Search icon
  static const IconData search = Icons.search;
  
  /// Filter icon
  static const IconData filter = Icons.tune;
  
  /// Share icon
  static const IconData share = Icons.share;
  
  /// Favorite icon (outline)
  static const IconData favorite = Icons.favorite_border;
  
  /// Favorite icon (filled)
  static const IconData favoriteFilled = Icons.favorite;
  
  /// More options icon
  static const IconData more = Icons.more_vert;

  // ============================================================================
  // COOKING & FOOD ICONS
  // ============================================================================
  
  /// Recipe icon
  static const IconData recipe = Icons.restaurant_menu;
  
  /// Cooking time icon
  static const IconData cookingTime = Icons.access_time;
  
  /// Difficulty icon
  static const IconData difficulty = Icons.bar_chart;
  
  /// Servings icon
  static const IconData servings = Icons.people;
  
  /// Ingredients icon
  static const IconData ingredients = Icons.list_alt;
  
  /// Chef hat icon
  static const IconData chef = Icons.restaurant;
  
  /// Utensils icon
  static const IconData utensils = Icons.restaurant;
  
  /// Oven icon
  static const IconData oven = Icons.kitchen;
  
  /// Timer icon
  static const IconData timer = Icons.timer;

  // ============================================================================
  // STATUS ICONS
  // ============================================================================
  
  /// Success/Check icon
  static const IconData success = Icons.check_circle;
  
  /// Error icon
  static const IconData error = Icons.error;
  
  /// Warning icon
  static const IconData warning = Icons.warning;
  
  /// Info icon
  static const IconData info = Icons.info;
  
  /// Star rating icon (outline)
  static const IconData star = Icons.star_border;
  
  /// Star rating icon (filled)
  static const IconData starFilled = Icons.star;

  // ============================================================================
  // UTILITY ICONS
  // ============================================================================
  
  /// Settings icon
  static const IconData settings = Icons.settings;
  
  /// Help icon
  static const IconData help = Icons.help;
  
  /// Notification icon
  static const IconData notification = Icons.notifications;
  
  /// Camera icon
  static const IconData camera = Icons.camera_alt;
  
  /// Gallery icon
  static const IconData gallery = Icons.photo_library;
  
  /// Download icon
  static const IconData download = Icons.download;
  
  /// Upload icon
  static const IconData upload = Icons.upload;
  
  /// Copy icon
  static const IconData copy = Icons.copy;
  
  /// Visibility icon
  static const IconData visibility = Icons.visibility;
  
  /// Visibility off icon
  static const IconData visibilityOff = Icons.visibility_off;

  // ============================================================================
  // HELPER METHODS
  // ============================================================================
  
  /// Get icon for difficulty level
  static IconData getDifficultyIcon(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return Icons.looks_one;
      case 'medium':
        return Icons.looks_two;
      case 'hard':
        return Icons.looks_3;
      default:
        return difficulty;
    }
  }
  
  /// Get icon for meal type
  static IconData getMealTypeIcon(String mealType) {
    switch (mealType.toLowerCase()) {
      case 'breakfast':
        return Icons.free_breakfast;
      case 'lunch':
        return Icons.lunch_dining;
      case 'dinner':
        return Icons.dinner_dining;
      case 'snack':
        return Icons.cookie;
      case 'dessert':
        return Icons.cake;
      default:
        return recipe;
    }
  }
  
  /// Get icon for cuisine type
  static IconData getCuisineIcon(String cuisine) {
    switch (cuisine.toLowerCase()) {
      case 'italian':
        return Icons.local_pizza;
      case 'asian':
        return Icons.ramen_dining;
      case 'mexican':
        return Icons.local_dining;
      case 'american':
        return Icons.lunch_dining;
      case 'french':
        return Icons.restaurant;
      default:
        return recipe;
    }
  }
}
