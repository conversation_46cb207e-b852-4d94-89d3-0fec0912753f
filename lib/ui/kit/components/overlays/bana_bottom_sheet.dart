import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../tokens/bana_colors.dart';
import '../../tokens/bana_typography.dart';
import '../../tokens/bana_spacing.dart';
import '../../tokens/bana_shadows.dart';
import '../../tokens/bana_borders.dart';
import '../buttons/bana_icon_button.dart';

/// BanaChef AI Bottom Sheet Component
/// 
/// Bottom sheet component following the BanaChef Design System.
/// Used for contextual actions and non-blocking interactions.
/// 
/// Usage:
/// ```dart
/// // Simple bottom sheet
/// BanaBottomSheet.show(
///   context: context,
///   title: 'Recipe Options',
///   child: RecipeOptionsContent(),
/// )
/// 
/// // Action sheet
/// BanaBottomSheet.actionSheet(
///   context: context,
///   title: 'Share Recipe',
///   actions: [
///     BanaBottomSheetAction(
///       icon: Icons.share,
///       title: 'Share Link',
///       onTap: () => shareLink(),
///     ),
///     BanaBottomSheetAction(
///       icon: Icons.copy,
///       title: 'Copy Link',
///       onTap: () => copyLink(),
///     ),
///   ],
/// )
/// ```
class BanaBottomSheet extends StatefulWidget {
  /// Bottom sheet title
  final String? title;
  
  /// Bottom sheet content
  final Widget child;
  
  /// Whether to show drag handle
  final bool showHandle;
  
  /// Whether to show close button
  final bool showCloseButton;
  
  /// Maximum height as fraction of screen height
  final double? maxHeightFraction;
  
  /// Initial height as fraction of screen height
  final double? initialHeightFraction;
  
  /// Whether sheet is scrollable
  final bool isScrollControlled;
  
  /// Whether sheet can be dismissed by dragging
  final bool enableDrag;
  
  /// Whether sheet can be dismissed by tapping outside
  final bool isDismissible;
  
  /// Custom background color
  final Color? backgroundColor;

  const BanaBottomSheet({
    super.key,
    this.title,
    required this.child,
    this.showHandle = true,
    this.showCloseButton = false,
    this.maxHeightFraction,
    this.initialHeightFraction,
    this.isScrollControlled = true,
    this.enableDrag = true,
    this.isDismissible = true,
    this.backgroundColor,
  });

  /// Show bottom sheet
  static Future<T?> show<T>({
    required BuildContext context,
    String? title,
    required Widget child,
    bool showHandle = true,
    bool showCloseButton = false,
    double? maxHeightFraction,
    double? initialHeightFraction,
    bool isScrollControlled = true,
    bool enableDrag = true,
    bool isDismissible = true,
    Color? backgroundColor,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      isScrollControlled: isScrollControlled,
      enableDrag: enableDrag,
      isDismissible: isDismissible,
      backgroundColor: Colors.transparent,
      builder: (context) => BanaBottomSheet(
        title: title,
        showHandle: showHandle,
        showCloseButton: showCloseButton,
        maxHeightFraction: maxHeightFraction,
        initialHeightFraction: initialHeightFraction,
        isScrollControlled: isScrollControlled,
        enableDrag: enableDrag,
        isDismissible: isDismissible,
        backgroundColor: backgroundColor,
        child: child,
      ),
    );
  }

  /// Show action sheet
  static Future<T?> actionSheet<T>({
    required BuildContext context,
    String? title,
    required List<BanaBottomSheetAction> actions,
    bool showHandle = true,
    bool isDismissible = true,
  }) {
    return show<T>(
      context: context,
      title: title,
      showHandle: showHandle,
      isDismissible: isDismissible,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: actions.map((action) => _buildActionItem(context, action)).toList(),
      ),
    );
  }

  static Widget _buildActionItem(BuildContext context, BanaBottomSheetAction action) {
    return InkWell(
      onTap: () {
        Navigator.of(context).pop();
        action.onTap();
      },
      child: Container(
        width: double.infinity,
        padding: BanaSpacing.all.lg,
        child: Row(
          children: [
            if (action.icon != null) ...[
              Icon(
                action.icon,
                size: 24,
                color: action.isDestructive ? BanaColors.error : BanaColors.text,
              ),
              BanaSpacing.horizontalSpacing.md,
            ],
            Expanded(
              child: Text(
                action.title,
                style: BanaTypography.bodyLarge.copyWith(
                  color: action.isDestructive ? BanaColors.error : BanaColors.text,
                ),
              ),
            ),
            if (action.trailing != null) action.trailing!,
          ],
        ),
      ),
    );
  }

  @override
  State<BanaBottomSheet> createState() => _BanaBottomSheetState();
}

class _BanaBottomSheetState extends State<BanaBottomSheet>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _slideAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onDismiss() {
    if (widget.isDismissible) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final maxHeight = widget.maxHeightFraction != null
        ? screenHeight * widget.maxHeightFraction!
        : screenHeight * 0.9;
    
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, screenHeight * _slideAnimation.value),
          child: Container(
            constraints: BoxConstraints(
              maxHeight: maxHeight,
            ),
            decoration: BoxDecoration(
              color: widget.backgroundColor ?? BanaColors.surface,
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(BanaBorders.radiusXL),
              ),
              boxShadow: BanaShadows.bottomSheet,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Handle and header
                _buildHeader(),
                
                // Content
                Flexible(
                  child: widget.child,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(
        top: widget.showHandle ? BanaSpacing.md : BanaSpacing.lg,
        left: BanaSpacing.lg,
        right: BanaSpacing.lg,
        bottom: widget.title != null ? BanaSpacing.md : 0,
      ),
      decoration: widget.title != null ? BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: BanaColors.borderLight,
            width: BanaBorders.widthThin,
          ),
        ),
      ) : null,
      child: Column(
        children: [
          // Drag handle
          if (widget.showHandle)
            Container(
              width: 40,
              height: 4,
              margin: EdgeInsets.only(bottom: widget.title != null ? BanaSpacing.md : 0),
              decoration: BoxDecoration(
                color: BanaColors.border,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          
          // Title and close button
          if (widget.title != null)
            Row(
              children: [
                Expanded(
                  child: Text(
                    widget.title!,
                    style: BanaTypography.title3,
                  ),
                ),
                
                if (widget.showCloseButton)
                  BanaIconButton(
                    icon: Icons.close,
                    onPressed: _onDismiss,
                    size: BanaIconButtonSize.small,
                    accessibilityLabel: 'Close bottom sheet',
                  ),
              ],
            ),
        ],
      ),
    );
  }
}

/// Bottom sheet action item
class BanaBottomSheetAction {
  /// Action icon
  final IconData? icon;
  
  /// Action title
  final String title;
  
  /// Action callback
  final VoidCallback onTap;
  
  /// Whether action is destructive
  final bool isDestructive;
  
  /// Trailing widget
  final Widget? trailing;

  const BanaBottomSheetAction({
    this.icon,
    required this.title,
    required this.onTap,
    this.isDestructive = false,
    this.trailing,
  });
}
