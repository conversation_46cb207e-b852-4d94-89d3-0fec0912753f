import 'package:flutter/material.dart';

/// BanaChef AI Color Palette
/// 
/// Comprehensive color system following the BanaChef Design System.
/// All colors are designed for optimal accessibility and brand consistency.
/// 
/// Usage:
/// ```dart
/// Container(
///   color: BanaColors.primary,
///   child: Text(
///     'Hello',
///     style: TextStyle(color: BanaColors.onPrimary),
///   ),
/// )
/// ```
class BanaColors {
  BanaColors._(); // Private constructor to prevent instantiation

  // ============================================================================
  // PRIMARY COLORS
  // ============================================================================
  
  /// Banana Yellow - Primary brand color
  /// Use for: Main CTAs, selected states, key highlights
  /// WCAG AA compliant with Deep Charcoal text
  static const Color primary = Color(0xFFFFD15C);
  
  /// Text color to use on primary background
  /// Deep Charcoal ensures WCAG AAA compliance on Banana Yellow
  static const Color onPrimary = Color(0xFF2D2D2D);

  // ============================================================================
  // TEXT COLORS
  // ============================================================================
  
  /// Deep Charcoal - Primary text color
  /// Use for: All main text content
  /// WCAG AAA compliant on white and light backgrounds
  static const Color text = Color(0xFF2D2D2D);
  
  /// Dark Grey - Secondary text color
  /// Use for: Captions, metadata, inactive elements
  /// WCAG AA compliant on white backgrounds
  static const Color textSecondary = Color(0xFF757575);
  
  /// Medium Grey - Tertiary text color
  /// Use for: Placeholder text, disabled states
  static const Color textTertiary = Color(0xFFBDBDBD);

  // ============================================================================
  // BACKGROUND COLORS
  // ============================================================================
  
  /// Pure White - Primary surface color
  /// Use for: Cards, modals, primary content areas
  static const Color surface = Color(0xFFFFFFFF);
  
  /// Light Grey - App background color
  /// Use for: Main app background to create contrast with white cards
  static const Color background = Color(0xFFF5F5F5);
  
  /// Very Light Grey - Secondary surface color
  /// Use for: Input fields, secondary content areas
  static const Color surfaceVariant = Color(0xFFFAFAFA);

  // ============================================================================
  // BORDER & DIVIDER COLORS
  // ============================================================================
  
  /// Medium Grey - Standard border color
  /// Use for: Input borders, card outlines, dividers
  static const Color border = Color(0xFFE0E0E0);
  
  /// Light Grey - Subtle border color
  /// Use for: Subtle dividers, section separators
  static const Color borderLight = Color(0xFFEEEEEE);
  
  /// Dark Grey - Focused border color
  /// Use for: Active input borders, selected card outlines
  static const Color borderDark = Color(0xFFBDBDBD);

  // ============================================================================
  // SYSTEM COLORS
  // ============================================================================
  
  /// Success Green - Success state color
  /// Use for: Success messages, completed states, positive actions
  static const Color success = Color(0xFF28A745);
  
  /// Text color for success backgrounds
  static const Color onSuccess = Color(0xFFFFFFFF);
  
  /// Error Red - Error state color
  /// Use for: Error messages, destructive actions, validation errors
  static const Color error = Color(0xFFDC3545);
  
  /// Text color for error backgrounds
  static const Color onError = Color(0xFFFFFFFF);
  
  /// Warning Yellow - Warning state color
  /// Use for: Warning messages, caution states
  static const Color warning = Color(0xFFFFC107);
  
  /// Text color for warning backgrounds
  static const Color onWarning = Color(0xFF2D2D2D);
  
  /// Info Blue - Information state color
  /// Use for: Info messages, neutral notifications
  static const Color info = Color(0xFF17A2B8);
  
  /// Text color for info backgrounds
  static const Color onInfo = Color(0xFFFFFFFF);

  // ============================================================================
  // INTERACTION COLORS
  // ============================================================================
  
  /// Pressed state color overlay
  /// Use for: Button pressed states (overlay on primary color)
  static const Color pressed = Color(0x1A000000); // 10% black overlay
  
  /// Hover state color overlay
  /// Use for: Button hover states (overlay on primary color)
  static const Color hover = Color(0x0D000000); // 5% black overlay
  
  /// Focus state color
  /// Use for: Focus indicators, accessibility highlights
  static const Color focus = primary;
  
  /// Disabled state color
  /// Use for: Disabled buttons, inactive elements
  static const Color disabled = Color(0xFFEEEEEE);
  
  /// Text color for disabled elements
  static const Color onDisabled = Color(0xFF9E9E9E);

  // ============================================================================
  // SEMANTIC COLORS
  // ============================================================================
  
  /// Selection background color
  /// Use for: Selected items in lists, active states
  static const Color selection = Color(0x1AFFD15C); // 10% primary overlay
  
  /// Highlight color for search results, etc.
  static const Color highlight = Color(0x33FFD15C); // 20% primary overlay
  
  /// Scrim color for overlays
  /// Use for: Modal backgrounds, bottom sheet overlays
  static const Color scrim = Color(0x80000000); // 50% black overlay

  // ============================================================================
  // GRADIENT COLORS
  // ============================================================================
  
  /// Primary gradient colors
  static const List<Color> primaryGradient = [
    Color(0xFFFFD15C), // Banana Yellow
    Color(0xFFFFC107), // Slightly darker yellow
  ];
  
  /// Success gradient colors
  static const List<Color> successGradient = [
    Color(0xFF28A745), // Success green
    Color(0xFF20C997), // Teal green
  ];

  // ============================================================================
  // ACCESSIBILITY HELPERS
  // ============================================================================
  
  /// Get appropriate text color for given background
  static Color getTextColorForBackground(Color backgroundColor) {
    // Calculate luminance to determine if background is light or dark
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? text : surface;
  }
  
  /// Check if color combination meets WCAG AA contrast requirements
  static bool meetsContrastRequirements(Color foreground, Color background) {
    final contrast = _calculateContrast(foreground, background);
    return contrast >= 4.5; // WCAG AA requirement
  }
  
  /// Calculate contrast ratio between two colors
  static double _calculateContrast(Color color1, Color color2) {
    final luminance1 = color1.computeLuminance();
    final luminance2 = color2.computeLuminance();
    final lighter = luminance1 > luminance2 ? luminance1 : luminance2;
    final darker = luminance1 > luminance2 ? luminance2 : luminance1;
    return (lighter + 0.05) / (darker + 0.05);
  }
}
