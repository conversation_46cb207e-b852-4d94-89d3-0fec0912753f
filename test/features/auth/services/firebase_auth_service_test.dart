import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:banachef/features/auth/services/firebase_auth_service.dart';
import 'package:banachef/core/errors/exceptions.dart';

import 'firebase_auth_service_test.mocks.dart';

@GenerateMocks([
  FirebaseAuth,
  GoogleSignIn,
  User,
  UserCredential,
  GoogleSignInAccount,
  GoogleSignInAuthentication,
])
void main() {
  group('FirebaseAuthService', () {
    late FirebaseAuthService firebaseAuthService;
    late MockFirebaseAuth mockFirebaseAuth;
    late MockGoogleSignIn mockGoogleSignIn;
    late MockUser mockUser;
    late MockUserCredential mockUserCredential;
    late MockGoogleSignInAccount mockGoogleSignInAccount;
    late MockGoogleSignInAuthentication mockGoogleSignInAuthentication;

    setUp(() {
      mockFirebaseAuth = MockFirebaseAuth();
      mockGoogleSignIn = MockGoogleSignIn();
      mockUser = MockUser();
      mockUserCredential = MockUserCredential();
      mockGoogleSignInAccount = MockGoogleSignInAccount();
      mockGoogleSignInAuthentication = MockGoogleSignInAuthentication();

      firebaseAuthService = FirebaseAuthService(
        firebaseAuth: mockFirebaseAuth,
        googleSignIn: mockGoogleSignIn,
      );
    });

    group('signInWithGoogle', () {
      test('should return Firebase ID token on successful Google sign-in', () async {
        // Arrange
        const testIdToken = 'test_firebase_id_token';
        const testAccessToken = 'test_access_token';
        const testGoogleIdToken = 'test_google_id_token';
        const testEmail = '<EMAIL>';

        when(mockGoogleSignIn.signIn()).thenAnswer((_) async => mockGoogleSignInAccount);
        when(mockGoogleSignInAccount.authentication).thenAnswer((_) async => mockGoogleSignInAuthentication);
        when(mockGoogleSignInAuthentication.accessToken).thenReturn(testAccessToken);
        when(mockGoogleSignInAuthentication.idToken).thenReturn(testGoogleIdToken);
        when(mockFirebaseAuth.signInWithCredential(any)).thenAnswer((_) async => mockUserCredential);
        when(mockUserCredential.user).thenReturn(mockUser);
        when(mockUser.getIdToken()).thenAnswer((_) async => testIdToken);
        when(mockUser.email).thenReturn(testEmail);

        // Act
        final result = await firebaseAuthService.signInWithGoogle();

        // Assert
        expect(result, equals(testIdToken));
        verify(mockGoogleSignIn.signIn()).called(1);
        verify(mockFirebaseAuth.signInWithCredential(any)).called(1);
        verify(mockUser.getIdToken()).called(1);
      });

      test('should throw AuthenticationException when Google sign-in is cancelled', () async {
        // Arrange
        when(mockGoogleSignIn.signIn()).thenAnswer((_) async => null);

        // Act & Assert
        expect(
          () => firebaseAuthService.signInWithGoogle(),
          throwsA(isA<AuthenticationException>()),
        );
      });

      test('should throw AuthenticationException when Google auth tokens are null', () async {
        // Arrange
        when(mockGoogleSignIn.signIn()).thenAnswer((_) async => mockGoogleSignInAccount);
        when(mockGoogleSignInAccount.authentication).thenAnswer((_) async => mockGoogleSignInAuthentication);
        when(mockGoogleSignInAuthentication.accessToken).thenReturn(null);
        when(mockGoogleSignInAuthentication.idToken).thenReturn(null);

        // Act & Assert
        expect(
          () => firebaseAuthService.signInWithGoogle(),
          throwsA(isA<AuthenticationException>()),
        );
      });

      test('should throw AuthenticationException when Firebase sign-in fails', () async {
        // Arrange
        const testAccessToken = 'test_access_token';
        const testGoogleIdToken = 'test_google_id_token';

        when(mockGoogleSignIn.signIn()).thenAnswer((_) async => mockGoogleSignInAccount);
        when(mockGoogleSignInAccount.authentication).thenAnswer((_) async => mockGoogleSignInAuthentication);
        when(mockGoogleSignInAuthentication.accessToken).thenReturn(testAccessToken);
        when(mockGoogleSignInAuthentication.idToken).thenReturn(testGoogleIdToken);
        when(mockFirebaseAuth.signInWithCredential(any)).thenAnswer((_) async => mockUserCredential);
        when(mockUserCredential.user).thenReturn(null);

        // Act & Assert
        expect(
          () => firebaseAuthService.signInWithGoogle(),
          throwsA(isA<AuthenticationException>()),
        );
      });

      test('should throw AuthenticationException when Firebase ID token is null', () async {
        // Arrange
        const testAccessToken = 'test_access_token';
        const testGoogleIdToken = 'test_google_id_token';

        when(mockGoogleSignIn.signIn()).thenAnswer((_) async => mockGoogleSignInAccount);
        when(mockGoogleSignInAccount.authentication).thenAnswer((_) async => mockGoogleSignInAuthentication);
        when(mockGoogleSignInAuthentication.accessToken).thenReturn(testAccessToken);
        when(mockGoogleSignInAuthentication.idToken).thenReturn(testGoogleIdToken);
        when(mockFirebaseAuth.signInWithCredential(any)).thenAnswer((_) async => mockUserCredential);
        when(mockUserCredential.user).thenReturn(mockUser);
        when(mockUser.getIdToken()).thenAnswer((_) async => null);

        // Act & Assert
        expect(
          () => firebaseAuthService.signInWithGoogle(),
          throwsA(isA<AuthenticationException>()),
        );
      });
    });

    group('signOut', () {
      test('should sign out from both Google and Firebase', () async {
        // Act
        await firebaseAuthService.signOut();

        // Assert
        verify(mockGoogleSignIn.signOut()).called(1);
        verify(mockFirebaseAuth.signOut()).called(1);
      });

      test('should throw AuthenticationException on sign out failure', () async {
        // Arrange
        when(mockFirebaseAuth.signOut()).thenThrow(Exception('Sign out failed'));

        // Act & Assert
        expect(
          () => firebaseAuthService.signOut(),
          throwsA(isA<AuthenticationException>()),
        );
      });
    });

    group('getCurrentUserIdToken', () {
      test('should return null when no user is signed in', () async {
        // Arrange
        when(mockFirebaseAuth.currentUser).thenReturn(null);

        // Act
        final result = await firebaseAuthService.getCurrentUserIdToken();

        // Assert
        expect(result, isNull);
      });

      test('should return ID token when user is signed in', () async {
        // Arrange
        const testIdToken = 'test_id_token';
        when(mockFirebaseAuth.currentUser).thenReturn(mockUser);
        when(mockUser.getIdToken()).thenAnswer((_) async => testIdToken);

        // Act
        final result = await firebaseAuthService.getCurrentUserIdToken();

        // Assert
        expect(result, equals(testIdToken));
        verify(mockUser.getIdToken()).called(1);
      });
    });

    group('isSignedIn', () {
      test('should return true when user is signed in', () {
        // Arrange
        when(mockFirebaseAuth.currentUser).thenReturn(mockUser);

        // Act
        final result = firebaseAuthService.isSignedIn;

        // Assert
        expect(result, isTrue);
      });

      test('should return false when no user is signed in', () {
        // Arrange
        when(mockFirebaseAuth.currentUser).thenReturn(null);

        // Act
        final result = firebaseAuthService.isSignedIn;

        // Assert
        expect(result, isFalse);
      });
    });

    group('isEmailVerified', () {
      test('should return true when user email is verified', () {
        // Arrange
        when(mockFirebaseAuth.currentUser).thenReturn(mockUser);
        when(mockUser.emailVerified).thenReturn(true);

        // Act
        final result = firebaseAuthService.isEmailVerified;

        // Assert
        expect(result, isTrue);
      });

      test('should return false when user email is not verified', () {
        // Arrange
        when(mockFirebaseAuth.currentUser).thenReturn(mockUser);
        when(mockUser.emailVerified).thenReturn(false);

        // Act
        final result = firebaseAuthService.isEmailVerified;

        // Assert
        expect(result, isFalse);
      });

      test('should return false when no user is signed in', () {
        // Arrange
        when(mockFirebaseAuth.currentUser).thenReturn(null);

        // Act
        final result = firebaseAuthService.isEmailVerified;

        // Assert
        expect(result, isFalse);
      });
    });
  });
}
